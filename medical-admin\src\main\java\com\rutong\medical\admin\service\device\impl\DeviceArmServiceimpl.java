package com.rutong.medical.admin.service.device.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rutong.medical.admin.dto.device.DeviceArmDTO;
import com.rutong.medical.admin.entity.device.DeviceArm;
import com.rutong.medical.admin.mapper.device.DeviceArmMapper;
import com.rutong.medical.admin.service.device.DeviceArmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2025-08-01
 */
@Service
public class DeviceArmServiceimpl extends ServiceImpl<DeviceArmMapper, DeviceArm> implements DeviceArmService {

    @Autowired
    private DeviceArmMapper deviceArmMapper;

    @Override
    public boolean isActive(DeviceArmDTO deviceArmDTO) {















        return deviceArmMapper.updateState(deviceArmDTO);
    }

    @Override
    public boolean autoArm(DeviceArmDTO deviceArmDTO) {















        return deviceArmMapper.autoArm(deviceArmDTO);
    }
}
