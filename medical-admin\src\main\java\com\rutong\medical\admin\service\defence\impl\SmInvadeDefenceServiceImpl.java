package com.rutong.medical.admin.service.defence.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.github.pagehelper.PageHelper;
import com.rutong.medical.admin.dto.defence.DefenceAndDeviceDTO;
import com.rutong.medical.admin.dto.defence.DefenceArmDTO;
import com.rutong.medical.admin.dto.defence.DefenceManageDTO;
import com.rutong.medical.admin.dto.defence.DefenceSaveOrUpdateDTO;
import com.rutong.medical.admin.entity.defence.SmInvadeDefence;
import com.rutong.medical.admin.entity.defence.SmDeviceLayoutDefence;
import com.rutong.medical.admin.mapper.defence.SmInvadeDefenceMapper;
import com.rutong.medical.admin.mapper.defence.SmDeviceLayoutDefenceMapper;
import com.rutong.medical.admin.service.defence.SmInvadeDefenceService;
import com.rutong.medical.admin.vo.defence.DefenceDeviceQueryVO;
import com.rutong.medical.admin.vo.defence.DefenceManageVO;
import com.rutong.medical.admin.vo.defence.SmInvadeDefenceVO;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.util.MyPageUtil;

import java.time.LocalDate;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025-07-21
 */
@Service
public class SmInvadeDefenceServiceImpl extends ServiceImpl<SmInvadeDefenceMapper, SmInvadeDefence> implements SmInvadeDefenceService {

    @Autowired
    private SmInvadeDefenceMapper smInvadeDefenceMapper;

    /**
     * 分页查询
     *
     * @param defenceManageDTO
     * @return
     */
    @Override
    public MyPageData<SmInvadeDefenceVO> page(DefenceManageDTO defenceManageDTO) {

        // 分页设置
        Integer pageNum = defenceManageDTO.getPageNum();
        Integer pageSize = defenceManageDTO.getPageSize();

        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }

        // 查询防区列表
        List<SmInvadeDefenceVO> list = smInvadeDefenceMapper.selectDefencePage(defenceManageDTO);

        // 使用MyPageUtil工具类转换为分页数据
        return MyPageUtil.makeResponseData(list);
    }

    @Autowired
    private SmDeviceLayoutDefenceMapper smDeviceLayoutDefenceMapper;

    /**
     * 新建/编辑 防区
     *
     * @param defenceSaveOrUpdateDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdate(DefenceSaveOrUpdateDTO defenceSaveOrUpdateDTO) {
        String defenceCode = defenceSaveOrUpdateDTO.getDefenceCode();
        String defenceName = defenceSaveOrUpdateDTO.getDefenceName();

        Long id = defenceSaveOrUpdateDTO.getInvadeDefenceId();
        List<Long> deviceIdss = defenceSaveOrUpdateDTO.getDeviceIdss();

        SmInvadeDefence smInvadeDefence = new SmInvadeDefence();
        smInvadeDefence.setId(id);
        smInvadeDefence.setDefenceCode(defenceCode);
        smInvadeDefence.setDefenceName(defenceName);
        smInvadeDefence.setDefenceState(0);

        // 使用DTO中的时间，如果没有则使用默认值
        smInvadeDefence.setStartTime(defenceSaveOrUpdateDTO.getStartTime() != null ?
            defenceSaveOrUpdateDTO.getStartTime() : LocalTime.of(20, 30));
        smInvadeDefence.setEndTime(defenceSaveOrUpdateDTO.getEndTime() != null ?
            defenceSaveOrUpdateDTO.getEndTime() : LocalTime.of(8, 0));

        //  TODO 创建人 - 应该从当前登录用户获取
        smInvadeDefence.setCreateUserId(1000L);
        smInvadeDefence.setCreateTime(LocalDate.now());
        // TODO 更新人 - 应该从当前登录用户获取
        smInvadeDefence.setUpdateUserId(1000L);
        smInvadeDefence.setUpdateTime(LocalDate.now());
        smInvadeDefence.setIsDelete(0);

        boolean defenceResult;
        Long defenceId;

        // 检查数据库中是否存在该id的记录
        if (id != null) {
            SmInvadeDefence existingRecord = smInvadeDefenceMapper.selectById(id);
            if (existingRecord != null) {
                // 更新防区信息
                defenceResult = SqlHelper.retBool(smInvadeDefenceMapper.updateById(smInvadeDefence));
                defenceId = id;

                // 更新设备关联：先删除原有关联，再插入新关联
                if (defenceResult) {
                    handleDeviceRelations(defenceId, deviceIdss, true);
                }

                return defenceResult;
            }
        }

        // 新建防区
        smInvadeDefence.setId(null); // 确保id为null，让数据库自动生成
        defenceResult = SqlHelper.retBool(smInvadeDefenceMapper.insert(smInvadeDefence));

        if (defenceResult) {
            defenceId = smInvadeDefence.getId(); // 获取新插入的防区ID
            // 处理设备关联
            handleDeviceRelations(defenceId, deviceIdss, false);
        }
        return defenceResult;
    }

    /**
     * 处理防区设备关联关系
     *
     * @param defenceId 防区ID
     * @param deviceIdss 设备列表
     * @param isUpdate 是否为更新操作
     */
    private void handleDeviceRelations(Long defenceId, List<Long> deviceIdss, boolean isUpdate) {
        if (defenceId == null) {
            return;
        }

        // 如果是更新操作，先删除原有的关联关系
        if (isUpdate) {
            LambdaQueryWrapper<SmDeviceLayoutDefence> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(SmDeviceLayoutDefence::getInvadeDefenceId, defenceId);
            smDeviceLayoutDefenceMapper.delete(deleteWrapper);
        }

        // 如果有设备列表，批量插入新的关联关系
        if (CollectionUtils.isNotEmpty(deviceIdss)) {
            List<SmDeviceLayoutDefence> relationList = new ArrayList<>();
            for (Long deviceIds : deviceIdss) {
                if (deviceIds != null) {
                    SmDeviceLayoutDefence relation = new SmDeviceLayoutDefence();
                    relation.setInvadeDefenceId(defenceId);
                    relation.setDeviceId(deviceIds);
                    relationList.add(relation);
                }
            }

            if (CollectionUtils.isNotEmpty(relationList)) {
                // 批量插入关联关系
                for (SmDeviceLayoutDefence relation : relationList) {
                    smDeviceLayoutDefenceMapper.insert(relation);
                }
            }
        }
    }

    /**
     * 解除关联
     *
     * @param defenceAndDeviceDTO 防区设备表ID
     * @return
     */
    @Override
    public boolean removeConnect(DefenceAndDeviceDTO defenceAndDeviceDTO) {

        // 删除防区设备关联记录
        boolean result = smInvadeDefenceMapper.deleteConnect(defenceAndDeviceDTO);

        return result;
    }

    /**
     * 布防/撤防/自动布防
     *
     * @param defenceArmDTO 布防操作参数
     * @return
     */
    @Override
    public Boolean updateDefenceArm(DefenceArmDTO defenceArmDTO) {
        // 参数校验
        if (CollectionUtils.isEmpty(defenceArmDTO.getDefenceIds())) {
            throw new IllegalArgumentException("防区ID列表不能为空");
        }

        if (defenceArmDTO.getOperationType() == null) {
            throw new IllegalArgumentException("操作类型不能为空");
        }

        // 根据操作类型进行不同的处理
        if (defenceArmDTO.getOperationType() == 1) {
            // 手动布防/撤防
            return handleManualArm(defenceArmDTO);
        } else if (defenceArmDTO.getOperationType() == 2) {
            // 自动布防
            return handleAutoArm(defenceArmDTO);
        } else {
            throw new IllegalArgumentException("操作类型无效，只支持1（手动布防/撤防）或2（自动布防）");
        }
    }

    /**
     * 处理手动布防/撤防
     *
     * @param defenceArmDTO
     * @return
     */
    private Boolean handleManualArm(DefenceArmDTO defenceArmDTO) {
        if (defenceArmDTO.getDefenceState() == null) {
            throw new IllegalArgumentException("手动操作时布防状态不能为空");
        }

        if (defenceArmDTO.getDefenceState() != 0 && defenceArmDTO.getDefenceState() != 1) {
            throw new IllegalArgumentException("布防状态只能是0（撤防）或1（布防）");
        }

        // 批量更新防区状态
        for (Long defenceId : defenceArmDTO.getDefenceIds()) {
            SmInvadeDefence defence = new SmInvadeDefence();
            defence.setId(defenceId);
            defence.setDefenceState(defenceArmDTO.getDefenceState());
            defence.setUpdateTime(LocalDate.now());
            // TODO: 设置更新用户ID
            // defence.setUpdateUserId(getCurrentUserId());

            // 如果是撤防，清空自动布防时间
            if (defenceArmDTO.getDefenceState() == 0) {

                defence.setStartTime(null);
                defence.setEndTime(null);
            }

            updateById(defence);
        }

        return true;
    }

    /**
     * 处理自动布防
     *
     * @param defenceArmDTO
     * @return
     */
    private Boolean handleAutoArm(DefenceArmDTO defenceArmDTO) {
        if (defenceArmDTO.getStartTime() == null || defenceArmDTO.getEndTime() == null) {
            throw new IllegalArgumentException("自动布防时开始时间和结束时间不能为空");
        }

        // 校验时间：开始时间不能比结束时间晚
        if (defenceArmDTO.getStartTime().isAfter(defenceArmDTO.getEndTime())) {
            throw new IllegalArgumentException("开始时间不能比结束时间晚");
        }

        // 批量设置自动布防
        for (Long defenceId : defenceArmDTO.getDefenceIds()) {
            SmInvadeDefence defence = new SmInvadeDefence();
            defence.setId(defenceId);
            defence.setDefenceState(1); // 自动布防默认设置为布防状态
            defence.setStartTime(defenceArmDTO.getStartTime());
            defence.setEndTime(defenceArmDTO.getEndTime());
            defence.setUpdateTime(LocalDate.now());
            // TODO: 设置更新用户ID
            // defence.setUpdateUserId(getCurrentUserId());

            updateById(defence);
        }

        return true;
    }

    /**
     * 批量删除防区
     *
     * @param defenceIds 防区ID列表
     * @return
     */
    @Override
    public Boolean batchDeleteDefence(List<Long> defenceIds) {
        if (CollectionUtils.isEmpty(defenceIds)) {
            throw new IllegalArgumentException("防区ID列表不能为空");
        }

        // 软删除：将is_delete字段更新为1
        for (Long defenceId : defenceIds) {
            SmInvadeDefence defence = new SmInvadeDefence();
            defence.setId(defenceId);
            defence.setIsDelete(1); // 1表示已删除
            defence.setUpdateTime(LocalDate.now()); // 更新修改时间
            updateById(defence);
        }

        return true;
    }


    /**
     * 防区详情
     *
     * @param defenceManageDTO
     * @return
     */
    @Override
    public MyPageData<DefenceManageVO> detail(DefenceManageDTO defenceManageDTO) {

        Integer pageNum = defenceManageDTO.getPageNum();
        Integer pageSize = defenceManageDTO.getPageSize();

        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }

        // 查询防区和设备的关联数据
        List<DefenceDeviceQueryVO> queryResults = smInvadeDefenceMapper.selectDevicePage(defenceManageDTO);

        // 构建结构化的返回数据
        DefenceManageVO result = buildDefenceManageVO(queryResults, defenceManageDTO.getInvadeDefenceId());

        // 包装成分页数据
        List<DefenceManageVO> resultList = Collections.singletonList(result);
        return MyPageUtil.makeResponseData(resultList);
    }

    /**
     * 构建DefenceManageVO对象
     *
     * @param queryResults 查询结果列表
     * @param defenceId 防区ID
     * @return DefenceManageVO
     */
    private DefenceManageVO buildDefenceManageVO(List<DefenceDeviceQueryVO> queryResults, Long defenceId) {
        DefenceManageVO result = new DefenceManageVO();

        // 构建基本信息
        DefenceManageVO.BasicInfo basicInfo = new DefenceManageVO.BasicInfo();

        if (CollectionUtils.isNotEmpty(queryResults)) {
            // 从第一条记录中获取基本信息（所有记录的基本信息都相同）
            DefenceDeviceQueryVO firstRecord = queryResults.get(0);
            basicInfo.setInvadeDefenceId(firstRecord.getInvadeDefenceId());
            basicInfo.setDefenceCode(firstRecord.getDefenceCode());
            basicInfo.setDefenceName(firstRecord.getDefenceName());

            // 构建设备列表（过滤掉设备ID为null的记录）
            List<DefenceManageVO.DeviceInfo> deviceList = queryResults.stream()
                    .filter(item -> item.getDeviceId() != null)
                    .map(item -> {
                        DefenceManageVO.DeviceInfo deviceInfo = new DefenceManageVO.DeviceInfo();
                        deviceInfo.setDeviceId(item.getDeviceId());
                        deviceInfo.setDeviceName(item.getDeviceName());
                        deviceInfo.setDeviceSn(item.getDeviceSn());
                        deviceInfo.setSpaceFullName(item.getSpaceFullName());
                        return deviceInfo;
                    })
                    .collect(Collectors.toList());

            result.setDeviceList(deviceList);
        } else {
            // 如果查询结果为空，从数据库直接获取防区基本信息
            SmInvadeDefence defence = smInvadeDefenceMapper.selectById(defenceId);
            if (defence != null) {
                basicInfo.setInvadeDefenceId(defence.getId());
                basicInfo.setDefenceCode(defence.getDefenceCode());
                basicInfo.setDefenceName(defence.getDefenceName());
            }

            // 设备列表为空
            result.setDeviceList(new ArrayList<>());
        }

        result.setBasicInfo(basicInfo);
        return result;
    }
}
