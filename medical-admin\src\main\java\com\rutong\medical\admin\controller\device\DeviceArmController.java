package com.rutong.medical.admin.controller.device;

import com.rutong.medical.admin.dto.device.DeviceArmDTO;
import com.rutong.medical.admin.service.device.DeviceArmService;
import com.soft.common.core.object.ResponseResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2025-08-01
 */

@RestController
@RequestMapping("/DeviceArm")
public class DeviceArmController {

    @Autowired
    private DeviceArmService deviceArmService;

    // 布防/撤防
    @PostMapping("/arm")
    @ApiOperation(value = "撤防/布防")
    public ResponseResult<Void> active(@RequestBody DeviceArmDTO deviceArmDTO) {
        // 获取用户ID
//        Long userId=TokenData.takeFromRequest().getUserId();

        boolean active = deviceArmService.isActive(deviceArmDTO);
        return ResponseResult.success();
    }

    // 自动布防
    @PostMapping("/autoArm")
    @ApiOperation(value="自动布防")
    public ResponseResult<Void> autoArm(@RequestBody DeviceArmDTO deviceArmDTO){

//        Long userId= TokenData.takeFromRequest().getUserId();

        boolean autoArm=deviceArmService.autoArm(deviceArmDTO);
        return ResponseResult.success();
    }


}
