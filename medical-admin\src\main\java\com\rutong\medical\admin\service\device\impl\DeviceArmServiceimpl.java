package com.rutong.medical.admin.service.device.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rutong.medical.admin.dto.device.DeviceArmDTO;
import com.rutong.medical.admin.entity.device.DeviceArm;
import com.rutong.medical.admin.mapper.device.DeviceArmMapper;
import com.rutong.medical.admin.service.device.DeviceArmService;
import com.soft.admin.upms.job.SysTaskInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-08-01
 */
@Slf4j
@Service
public class DeviceArmServiceimpl extends ServiceImpl<DeviceArmMapper, DeviceArm> implements DeviceArmService {

    @Autowired
    private DeviceArmMapper deviceArmMapper;

    @Resource
    private SysTaskInfoService sysTaskInfoService;

    @Override
    public boolean isActive(DeviceArmDTO deviceArmDTO) {
        // 手动布防/撤防，清除自动布防时间设置
        return deviceArmMapper.updateState(deviceArmDTO);
    }

    @Override
    public boolean autoArm(DeviceArmDTO deviceArmDTO) {
        try {
            // 更新设备布防时间配置
            boolean result = deviceArmMapper.autoArm(deviceArmDTO);

//            if (result) {
//                // 创建或更新定时任务
//                createAutoArmScheduleTask(deviceArmDTO);
//            }

            return result;
        } catch (Exception e) {
            log.error("设置自动布防失败", e);
            return false;
        }
    }

//    /**
//     * 创建自动布防定时任务
//     */
//    private void createAutoArmScheduleTask(DeviceArmDTO deviceArmDTO) {
//        try {
//            String executorHandler = "autoDeviceArmHandler";
//            String jobDesc = "设备自动布防任务-设备ID:" + deviceArmDTO.getDeviceId();
//
//            // 每分钟执行一次检查
//            String cronExpression = "0 * * * * ?";
//
//            // 任务参数
//            JSONObject params = new JSONObject();
//            params.put("deviceId", deviceArmDTO.getDeviceId());
//            params.put("startTime", deviceArmDTO.getStartTime().toString());
//            params.put("endTime", deviceArmDTO.getEndTime().toString());
//
//            // 创建定时任务
//            sysTaskInfoService.addTask(jobDesc, cronExpression, executorHandler, params);
//
//            log.info("创建自动布防定时任务成功，设备ID: {}", deviceArmDTO.getDeviceId());
//        } catch (Exception e) {
//            log.error("创建自动布防定时任务失败", e);
//        }
//    }

    /**
     * 检查并更新设备布防状态
     */
    public void checkAndUpdateDeviceArmStatus() {
        try {
            // 查询所有配置了自动布防的设备
            LambdaQueryWrapper<DeviceArm> queryWrapper = Wrappers.lambdaQuery(DeviceArm.class);
            queryWrapper.isNotNull(DeviceArm::getStartTime)
                       .isNotNull(DeviceArm::getEndTime);

            List<DeviceArm> deviceArms = this.list(queryWrapper);

            LocalTime currentTime = LocalTime.now();

            for (DeviceArm deviceArm : deviceArms) {
                LocalTime startTime = deviceArm.getStartTime();
                LocalTime endTime = deviceArm.getEndTime();

                boolean shouldBeArmed = isTimeInRange(currentTime, startTime, endTime);
                int targetState = shouldBeArmed ? 1 : 0; // 1-布防，0-撤防

                // 如果当前状态与目标状态不一致，则更新
                if (deviceArm.getDefenceState() == null || !deviceArm.getDefenceState().equals(targetState)) {
                    DeviceArmDTO updateDTO = new DeviceArmDTO();
                    updateDTO.setId(deviceArm.getId());
                    updateDTO.setDefenceState(targetState);

                    deviceArmMapper.updateState(updateDTO);

                    log.info("自动更新设备布防状态，设备ID: {}, 新状态: {}",
                            deviceArm.getDeviceId(), targetState == 1 ? "布防" : "撤防");
                }
            }
        } catch (Exception e) {
            log.error("检查并更新设备布防状态失败", e);
        }
    }

    /**
     * 判断当前时间是否在指定时间范围内
     */
    private boolean isTimeInRange(LocalTime currentTime, LocalTime startTime, LocalTime endTime) {
        if (startTime.isBefore(endTime)) {
            // 同一天内的时间范围，如 09:00 - 18:00
            return !currentTime.isBefore(startTime) && !currentTime.isAfter(endTime);
        } else {
            // 跨天的时间范围，如 22:00 - 06:00
            return !currentTime.isBefore(startTime) || !currentTime.isAfter(endTime);
        }
    }
}
