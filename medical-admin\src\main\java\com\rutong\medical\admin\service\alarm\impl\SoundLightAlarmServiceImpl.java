package com.rutong.medical.admin.service.alarm.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rutong.medical.admin.dto.alarm.SoundLightAlarmDTO;
import com.rutong.medical.admin.entity.alarm.SmAlarmConfig;
import com.rutong.medical.admin.mapper.alarm.SoundLightAlarmMapper;
import com.rutong.medical.admin.service.alarm.SoundLightAlarmService;
import com.rutong.medical.admin.vo.alarm.SoundLightAlarmVO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025-07-15
 * 声光报警设置服务实现类
 */
@Slf4j
@Service
public class SoundLightAlarmServiceImpl extends ServiceImpl<SoundLightAlarmMapper, SmAlarmConfig> implements SoundLightAlarmService {

    /**
     * 缓存相关常量
     */
    private static final String ALARM_CONFIG_CACHE_KEY = "alarm:config:all";
    private static final String ALARM_CONFIG_BY_CODE_TYPE_CACHE_KEY = "alarm:config:code:type:";
    private static final int REDIS_CACHE_EXPIRE_SECONDS = 1800; // Redis缓存30分钟
    private static final String CAFFEINE_CACHE_NAME = "GLOBAL_CACHE";


    @Autowired
    private RedissonClient redissonClient;

    @Resource(name = "caffeineCacheManager")
    private CacheManager cacheManager;

    /**
     * 更新报警配置
     *
     * @param soundLightAlarmDTO 报警配置DTO
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateAlarmConfig(SoundLightAlarmDTO soundLightAlarmDTO) {
        if (soundLightAlarmDTO == null || soundLightAlarmDTO.getConfigCode() == null ||
                soundLightAlarmDTO.getConfigType() == null || soundLightAlarmDTO.getConfigValue() == null) {
            throw new IllegalArgumentException("配置编码、配置类型和配置值都不能为空");
        }
        try {
            LambdaQueryWrapper<SmAlarmConfig> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SmAlarmConfig::getConfigCode, soundLightAlarmDTO.getConfigCode())
                    .eq(SmAlarmConfig::getConfigType, soundLightAlarmDTO.getConfigType());
            SmAlarmConfig updateConfig = new SmAlarmConfig();
            updateConfig.setConfigValue(soundLightAlarmDTO.getConfigValue());
            updateConfig.setUpdateUserId(soundLightAlarmDTO.getUpdateUserId() != null ?
                    soundLightAlarmDTO.getUpdateUserId() : 123456L);
            updateConfig.setUpdateTime(new Date());

            this.update(updateConfig, queryWrapper);

            // 更新后清除所有相关缓存
            clearAllAlarmConfigCache();
            return true;
        } catch (Exception e) {
            log.error("更新报警配置失败", e);
            throw new RuntimeException("更新报警配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有报警配置（带三级缓存）
     *
     * @return 所有报警配置列表
     */
    @Override
    public List<SoundLightAlarmVO> getAllAlarmConfigs() {
        // 第一级：检查Caffeine本地缓存
        Cache caffeineCache = cacheManager.getCache(CAFFEINE_CACHE_NAME);
        if (caffeineCache != null) {
            Cache.ValueWrapper wrapper = caffeineCache.get(ALARM_CONFIG_CACHE_KEY);
            if (wrapper != null && wrapper.get() != null) {
                log.debug("从Caffeine本地缓存获取报警配置数据");
                return (List<SoundLightAlarmVO>) wrapper.get();
            }
        }

        // 第二级：检查Redis分布式缓存
        RBucket<List<SoundLightAlarmVO>> redisBucket = redissonClient.getBucket(ALARM_CONFIG_CACHE_KEY);
        if (redisBucket.isExists()) {
            List<SoundLightAlarmVO> configList = redisBucket.get();
            if (configList != null && !configList.isEmpty()) {
                log.debug("从Redis分布式缓存获取报警配置数据");
                // 将数据存入Caffeine本地缓存
                if (caffeineCache != null) {
                    caffeineCache.put(ALARM_CONFIG_CACHE_KEY, configList);
                }
                return configList;
            }
        }

        // 第三级：从MySQL数据库查询
        log.debug("从MySQL数据库查询报警配置数据");
        List<SoundLightAlarmVO> configList = this.list().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        // 将数据存入Redis缓存
        if (!configList.isEmpty()) {
            redisBucket.set(configList, REDIS_CACHE_EXPIRE_SECONDS, TimeUnit.SECONDS);
            log.debug("报警配置数据已存入Redis缓存，过期时间：{}秒", REDIS_CACHE_EXPIRE_SECONDS);
        }

        // 将数据存入Caffeine本地缓存
        if (caffeineCache != null && !configList.isEmpty()) {
            caffeineCache.put(ALARM_CONFIG_CACHE_KEY, configList);
            log.debug("报警配置数据已存入Caffeine本地缓存");
        }

        return configList;
    }

    /**
     * 清除所有报警配置相关的缓存
     */
    private void clearAllAlarmConfigCache() {
        try {
            // 清除Caffeine本地缓存
            Cache caffeineCache = cacheManager.getCache(CAFFEINE_CACHE_NAME);
            if (caffeineCache != null) {
                // 清除所有配置缓存
                caffeineCache.evict(ALARM_CONFIG_CACHE_KEY);

                // 由于无法遍历所有可能的code和type组合，这里采用清除整个缓存的方式
                caffeineCache.clear();
                log.debug("已清除Caffeine本地缓存中的报警配置数据");
            }

            // 清除Redis分布式缓存
            redissonClient.getBucket(ALARM_CONFIG_CACHE_KEY).deleteAsync();

            // 清除Redis中所有相关的缓存键（使用模式匹配）
            redissonClient.getKeys().deleteByPatternAsync(ALARM_CONFIG_BY_CODE_TYPE_CACHE_KEY + "*");
            log.debug("已清除Redis分布式缓存中的报警配置数据");

        } catch (Exception e) {
            log.error("清除报警配置缓存时发生异常", e);
        }
    }

    /**
     * 将实体转换为VO
     *
     * @param config 实体
     * @return VO
     */
    private SoundLightAlarmVO convertToVO(SmAlarmConfig config) {
        SoundLightAlarmVO vo = new SoundLightAlarmVO();
        BeanUtils.copyProperties(config, vo);
        return vo;
    }
}