package com.rutong.medical.admin.service.alarm;

import com.rutong.medical.admin.dto.alarm.SoundLightAlarmDTO;
import com.rutong.medical.admin.vo.alarm.SoundLightAlarmVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-07-15
 * 声光报警设置服务接口
 */
public interface SoundLightAlarmService {

    /**
     * 更新报警配置
     *
     * @param soundLightAlarmDTO 报警配置DTO
     * @return 更新结果
     */
    Boolean updateAlarmConfig(SoundLightAlarmDTO soundLightAlarmDTO);

    /**
     * 获取所有报警配置（带三级缓存）
     *
     * @return 所有报警配置列表
     */
    List<SoundLightAlarmVO> getAllAlarmConfigs();
}